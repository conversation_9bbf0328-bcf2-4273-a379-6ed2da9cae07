import 'dart:math';
import 'dart:typed_data';
import 'package:flutter/material.dart';
import 'package:camera/camera.dart';
import 'package:tflite_flutter/tflite_flutter.dart';
import 'package:image/image.dart' as img;
import 'tts_service.dart';

/// Classe pour représenter un objet détecté
class DetectedObject {
  final String className;
  final double confidence;
  final double x;
  final double y;
  final double width;
  final double height;

  DetectedObject({
    required this.className,
    required this.confidence,
    required this.x,
    required this.y,
    required this.width,
    required this.height,
  });

  /// Nom de la classe en français
  String get classNameFr {
    const translations = {
      'person': 'Personne',
      'chair': 'Chaise',
      'table': 'Table',
      'cup': 'Tasse',
      'bottle': 'Bouteille',
      'cell phone': 'Téléphone portable',
      'book': 'Livre',
      'laptop': 'Ordinateur portable',
      'car': 'Voiture',
      'bicycle': 'Vélo',
      'dog': 'Chi<PERSON>',
      'cat': 'Chat',
      'tv': 'Télévision',
      'mouse': 'Souris',
      'keyboard': 'Clavier',
      'clock': 'Horloge',
    };
    return translations[className.toLowerCase()] ?? className;
  }

  /// Pourcentage de confiance
  String get confidencePercentage {
    return '${(confidence * 100).toInt()}%';
  }

  @override
  String toString() {
    return 'DetectedObject(className: $className, confidence: ${(confidence * 100).toInt()}%)';
  }
}

/// Service de reconnaissance d'objets avec TensorFlow Lite
class ObjectRecognitionService extends ChangeNotifier {
  static final ObjectRecognitionService _instance =
      ObjectRecognitionService._internal();
  factory ObjectRecognitionService() => _instance;
  ObjectRecognitionService._internal();

  // Contrôleur de caméra
  CameraController? _cameraController;
  CameraController? get cameraController => _cameraController;

  // Modèle TensorFlow Lite
  Interpreter? _interpreter;
  List<String> _labels = [];

  // Service TTS pour les annonces vocales
  final TtsService _ttsService = TtsService();

  // État du service
  bool _isInitialized = false;
  bool _isDetecting = false;
  bool _isModelLoaded = false;

  // Résultats de détection
  List<DetectedObject> _detectedObjects = [];
  List<DetectedObject> get detectedObjects => _detectedObjects;

  // Configuration du modèle
  static const int _inputSize = 320;
  static const double _threshold = 0.5;

  // Gestion des annonces vocales
  DateTime _lastAnnouncementTime = DateTime.now();
  String _lastAnnouncedObject = '';
  static const Duration _announcementCooldown = Duration(seconds: 3);

  // Getters pour l'état
  bool get isInitialized => _isInitialized;
  bool get isDetecting => _isDetecting;
  bool get isModelLoaded => _isModelLoaded;
  bool get isCameraReady => _cameraController?.value.isInitialized ?? false;

  /// Initialise le service de reconnaissance d'objets
  Future<bool> initialize() async {
    if (_isInitialized) return true;

    try {
      debugPrint(
        'Initialisation du service de détection d\'objets TensorFlow Lite...',
      );

      // Charger le modèle TensorFlow Lite
      await _loadModel();

      // Initialiser la caméra
      await _initializeCamera();

      _isInitialized = true;
      notifyListeners();
      debugPrint(
        'Service de détection d\'objets TensorFlow Lite initialisé avec succès',
      );
      return true;
    } catch (e) {
      debugPrint(
        'Erreur lors de l\'initialisation du service de reconnaissance: $e',
      );
      return false;
    }
  }

  /// Charge le modèle TensorFlow Lite
  Future<void> _loadModel() async {
    try {
      debugPrint('Chargement du modèle TensorFlow Lite...');

      // Charger le modèle depuis les assets
      _interpreter = await Interpreter.fromAsset('assets/models/model.tflite');

      // Charger les labels COCO
      _labels = _getCocoLabels();

      _isModelLoaded = true;
      debugPrint('Modèle TensorFlow Lite chargé avec succès');
      debugPrint('Nombre de labels: ${_labels.length}');

      // Afficher les informations du modèle
      final inputTensors = _interpreter!.getInputTensors();
      final outputTensors = _interpreter!.getOutputTensors();
      debugPrint('Input tensors: ${inputTensors.length}');
      debugPrint('Output tensors: ${outputTensors.length}');

      if (inputTensors.isNotEmpty) {
        debugPrint('Input shape: ${inputTensors[0].shape}');
      }
      if (outputTensors.isNotEmpty) {
        debugPrint('Output shape: ${outputTensors[0].shape}');
      }

      notifyListeners();
    } catch (e) {
      debugPrint('Erreur lors du chargement du modèle TensorFlow Lite: $e');
      _isModelLoaded = false;
      notifyListeners();
      rethrow;
    }
  }

  /// Retourne les labels COCO (80 classes)
  List<String> _getCocoLabels() {
    return [
      'person',
      'bicycle',
      'car',
      'motorcycle',
      'airplane',
      'bus',
      'train',
      'truck',
      'boat',
      'traffic light',
      'fire hydrant',
      'stop sign',
      'parking meter',
      'bench',
      'bird',
      'cat',
      'dog',
      'horse',
      'sheep',
      'cow',
      'elephant',
      'bear',
      'zebra',
      'giraffe',
      'backpack',
      'umbrella',
      'handbag',
      'tie',
      'suitcase',
      'frisbee',
      'skis',
      'snowboard',
      'sports ball',
      'kite',
      'baseball bat',
      'baseball glove',
      'skateboard',
      'surfboard',
      'tennis racket',
      'bottle',
      'wine glass',
      'cup',
      'fork',
      'knife',
      'spoon',
      'bowl',
      'banana',
      'apple',
      'sandwich',
      'orange',
      'broccoli',
      'carrot',
      'hot dog',
      'pizza',
      'donut',
      'cake',
      'chair',
      'couch',
      'potted plant',
      'bed',
      'dining table',
      'toilet',
      'tv',
      'laptop',
      'mouse',
      'remote',
      'keyboard',
      'cell phone',
      'microwave',
      'oven',
      'toaster',
      'sink',
      'refrigerator',
      'book',
      'clock',
      'vase',
      'scissors',
      'teddy bear',
      'hair drier',
      'toothbrush',
    ];
  }

  /// Initialise la caméra
  Future<void> _initializeCamera() async {
    try {
      final cameras = await availableCameras();
      if (cameras.isEmpty) {
        throw Exception('Aucune caméra disponible');
      }

      // Utiliser la caméra arrière par défaut
      final camera = cameras.firstWhere(
        (camera) => camera.lensDirection == CameraLensDirection.back,
        orElse: () => cameras.first,
      );

      _cameraController = CameraController(
        camera,
        ResolutionPreset.medium,
        enableAudio: false,
        imageFormatGroup: ImageFormatGroup.yuv420,
      );

      await _cameraController!.initialize();
      notifyListeners();
      debugPrint('Caméra initialisée avec succès');
    } catch (e) {
      debugPrint('Erreur lors de l\'initialisation de la caméra: $e');
      rethrow;
    }
  }

  /// Démarre la détection en temps réel
  Future<void> startDetection() async {
    if (!_isInitialized || _isDetecting || !isCameraReady) return;

    _isDetecting = true;
    notifyListeners();

    try {
      await _cameraController!.startImageStream(_processImage);
      debugPrint('Détection TensorFlow Lite en temps réel démarrée');
    } catch (e) {
      debugPrint('Erreur lors du démarrage de la détection: $e');
      _isDetecting = false;
      notifyListeners();
    }
  }

  /// Arrête la détection
  Future<void> stopDetection() async {
    if (!_isDetecting) return;

    try {
      await _cameraController?.stopImageStream();
      _isDetecting = false;
      _detectedObjects.clear();
      notifyListeners();
      debugPrint('Détection TensorFlow Lite en temps réel arrêtée');
    } catch (e) {
      debugPrint('Erreur lors de l\'arrêt de la détection: $e');
    }
  }

  /// Traite une image de la caméra pour la détection TensorFlow Lite
  void _processImage(CameraImage cameraImage) async {
    if (!_isModelLoaded || _interpreter == null) return;

    try {
      // Convertir l'image de la caméra en format utilisable
      final inputImage = _preprocessCameraImage(cameraImage);

      // Exécuter l'inférence TensorFlow Lite
      final detections = await _runInference(inputImage);

      // Mettre à jour les résultats
      _detectedObjects = detections;

      // Annoncer vocalement les objets détectés
      await _announceDetectedObjects(detections);

      notifyListeners();
    } catch (e) {
      debugPrint('Erreur lors du traitement de l\'image TensorFlow Lite: $e');
    }
  }

  /// Préprocesse l'image de la caméra pour TensorFlow Lite
  Float32List _preprocessCameraImage(CameraImage cameraImage) {
    // Convertir YUV420 en RGB
    final rgbImage = _convertYUV420ToRGB(cameraImage);

    // Redimensionner l'image
    final resizedImage = img.copyResize(
      rgbImage,
      width: _inputSize,
      height: _inputSize,
    );

    // Normaliser les pixels (0-1)
    final input = Float32List(_inputSize * _inputSize * 3);
    int pixelIndex = 0;

    for (int y = 0; y < _inputSize; y++) {
      for (int x = 0; x < _inputSize; x++) {
        final pixel = resizedImage.getPixel(x, y);
        input[pixelIndex++] = pixel.r / 255.0;
        input[pixelIndex++] = pixel.g / 255.0;
        input[pixelIndex++] = pixel.b / 255.0;
      }
    }

    return input;
  }

  /// Convertit YUV420 en RGB
  img.Image _convertYUV420ToRGB(CameraImage cameraImage) {
    final width = cameraImage.width;
    final height = cameraImage.height;

    final yPlane = cameraImage.planes[0];
    final uPlane = cameraImage.planes[1];
    final vPlane = cameraImage.planes[2];

    final image = img.Image(width: width, height: height);

    for (int y = 0; y < height; y++) {
      for (int x = 0; x < width; x++) {
        final yIndex = y * yPlane.bytesPerRow + x;
        final uvIndex = (y ~/ 2) * uPlane.bytesPerRow + (x ~/ 2);

        if (yIndex < yPlane.bytes.length && uvIndex < uPlane.bytes.length) {
          final yValue = yPlane.bytes[yIndex];
          final uValue = uPlane.bytes[uvIndex];
          final vValue = vPlane.bytes[uvIndex];

          // Conversion YUV vers RGB
          final r = (yValue + 1.402 * (vValue - 128)).clamp(0, 255).toInt();
          final g =
              (yValue - 0.344 * (uValue - 128) - 0.714 * (vValue - 128))
                  .clamp(0, 255)
                  .toInt();
          final b = (yValue + 1.772 * (uValue - 128)).clamp(0, 255).toInt();

          image.setPixel(x, y, img.ColorRgb8(r, g, b));
        }
      }
    }

    return image;
  }

  /// Exécute l'inférence TensorFlow Lite
  Future<List<DetectedObject>> _runInference(Float32List inputImage) async {
    if (_interpreter == null) return [];

    try {
      // Préparer les tenseurs d'entrée
      final input = inputImage.reshape([1, _inputSize, _inputSize, 3]);

      // Préparer les tenseurs de sortie
      final outputs = <int, Object>{};
      outputs[0] = Float32List(1 * 2535 * 85); // Ajuster selon votre modèle

      // Exécuter l'inférence
      _interpreter!.runForMultipleInputs([input], outputs);

      // Post-traiter les résultats
      return _postProcessResults(outputs[0] as Float32List);
    } catch (e) {
      debugPrint('Erreur lors de l\'inférence TensorFlow Lite: $e');
      return [];
    }
  }

  /// Post-traite les résultats de l'inférence
  List<DetectedObject> _postProcessResults(Float32List rawOutput) {
    final detections = <DetectedObject>[];

    try {
      final numDetections =
          rawOutput.length ~/ 85; // 85 = 4 (bbox) + 1 (conf) + 80 (classes)

      for (int i = 0; i < numDetections && detections.length < 5; i++) {
        final offset = i * 85;

        if (offset + 84 < rawOutput.length) {
          final centerX = rawOutput[offset];
          final centerY = rawOutput[offset + 1];
          final width = rawOutput[offset + 2];
          final height = rawOutput[offset + 3];
          final objectConfidence = rawOutput[offset + 4];

          if (objectConfidence > _threshold) {
            // Trouver la classe avec la plus haute probabilité
            double maxClassProb = 0;
            int maxClassIndex = 0;

            for (int j = 0; j < 80; j++) {
              final classProb = rawOutput[offset + 5 + j];
              if (classProb > maxClassProb) {
                maxClassProb = classProb;
                maxClassIndex = j;
              }
            }

            final finalConfidence = objectConfidence * maxClassProb;

            if (finalConfidence > _threshold &&
                maxClassIndex < _labels.length) {
              detections.add(
                DetectedObject(
                  className: _labels[maxClassIndex],
                  confidence: finalConfidence,
                  x: centerX,
                  y: centerY,
                  width: width,
                  height: height,
                ),
              );
            }
          }
        }
      }

      // Trier par confiance décroissante
      detections.sort((a, b) => b.confidence.compareTo(a.confidence));
    } catch (e) {
      debugPrint('Erreur lors du post-traitement: $e');
    }

    return detections;
  }

  /// Annonce vocalement les objets détectés
  Future<void> _announceDetectedObjects(List<DetectedObject> detections) async {
    if (detections.isEmpty) return;

    final now = DateTime.now();

    // Prendre l'objet avec la plus haute confiance
    final bestDetection = detections.first;
    final objectName = _translateToFrench(bestDetection.className);

    // Éviter les annonces trop fréquentes du même objet
    if (now.difference(_lastAnnouncementTime) > _announcementCooldown &&
        _lastAnnouncedObject != objectName) {
      try {
        await _ttsService.speak('Je vois $objectName');
        _lastAnnouncementTime = now;
        _lastAnnouncedObject = objectName;
        debugPrint('Annonce vocale: $objectName');
      } catch (e) {
        debugPrint('Erreur lors de l\'annonce vocale: $e');
      }
    }
  }

  /// Traduit les noms d'objets en français
  String _translateToFrench(String englishName) {
    const translations = {
      'person': 'une personne',
      'bicycle': 'un vélo',
      'car': 'une voiture',
      'motorcycle': 'une moto',
      'airplane': 'un avion',
      'bus': 'un bus',
      'train': 'un train',
      'truck': 'un camion',
      'boat': 'un bateau',
      'traffic light': 'un feu de circulation',
      'fire hydrant': 'une bouche d\'incendie',
      'stop sign': 'un panneau stop',
      'parking meter': 'un parcmètre',
      'bench': 'un banc',
      'bird': 'un oiseau',
      'cat': 'un chat',
      'dog': 'un chien',
      'horse': 'un cheval',
      'sheep': 'un mouton',
      'cow': 'une vache',
      'elephant': 'un éléphant',
      'bear': 'un ours',
      'zebra': 'un zèbre',
      'giraffe': 'une girafe',
      'backpack': 'un sac à dos',
      'umbrella': 'un parapluie',
      'handbag': 'un sac à main',
      'tie': 'une cravate',
      'suitcase': 'une valise',
      'frisbee': 'un frisbee',
      'skis': 'des skis',
      'snowboard': 'un snowboard',
      'sports ball': 'un ballon',
      'kite': 'un cerf-volant',
      'baseball bat': 'une batte de baseball',
      'baseball glove': 'un gant de baseball',
      'skateboard': 'un skateboard',
      'surfboard': 'une planche de surf',
      'tennis racket': 'une raquette de tennis',
      'bottle': 'une bouteille',
      'wine glass': 'un verre à vin',
      'cup': 'une tasse',
      'fork': 'une fourchette',
      'knife': 'un couteau',
      'spoon': 'une cuillère',
      'bowl': 'un bol',
      'banana': 'une banane',
      'apple': 'une pomme',
      'sandwich': 'un sandwich',
      'orange': 'une orange',
      'broccoli': 'du brocoli',
      'carrot': 'une carotte',
      'hot dog': 'un hot-dog',
      'pizza': 'une pizza',
      'donut': 'un donut',
      'cake': 'un gâteau',
      'chair': 'une chaise',
      'couch': 'un canapé',
      'potted plant': 'une plante en pot',
      'bed': 'un lit',
      'dining table': 'une table à manger',
      'toilet': 'des toilettes',
      'tv': 'une télévision',
      'laptop': 'un ordinateur portable',
      'mouse': 'une souris',
      'remote': 'une télécommande',
      'keyboard': 'un clavier',
      'cell phone': 'un téléphone portable',
      'microwave': 'un micro-ondes',
      'oven': 'un four',
      'toaster': 'un grille-pain',
      'sink': 'un évier',
      'refrigerator': 'un réfrigérateur',
      'book': 'un livre',
      'clock': 'une horloge',
      'vase': 'un vase',
      'scissors': 'des ciseaux',
      'teddy bear': 'un ours en peluche',
      'hair drier': 'un sèche-cheveux',
      'toothbrush': 'une brosse à dents',
    };

    return translations[englishName.toLowerCase()] ?? englishName;
  }

  /// Prend une photo et effectue la détection
  Future<List<DetectedObject>> detectFromPhoto() async {
    if (!isCameraReady || !_isModelLoaded || _interpreter == null) {
      return [];
    }

    try {
      // Prendre une photo
      final image = await _cameraController!.takePicture();
      debugPrint('Photo prise pour la détection TensorFlow Lite');

      // Lire et traiter l'image
      final imageBytes = await image.readAsBytes();
      final decodedImage = img.decodeImage(imageBytes);

      if (decodedImage != null) {
        // Préprocesser l'image
        final resizedImage = img.copyResize(
          decodedImage,
          width: _inputSize,
          height: _inputSize,
        );
        final inputData = _imageToFloat32List(resizedImage);

        // Exécuter l'inférence
        final detections = await _runInference(inputData);
        return detections;
      }

      return [];
    } catch (e) {
      debugPrint('Erreur lors de la détection sur photo: $e');
      return [];
    }
  }

  /// Convertit une image en Float32List
  Float32List _imageToFloat32List(img.Image image) {
    final input = Float32List(_inputSize * _inputSize * 3);
    int pixelIndex = 0;

    for (int y = 0; y < _inputSize; y++) {
      for (int x = 0; x < _inputSize; x++) {
        final pixel = image.getPixel(x, y);
        input[pixelIndex++] = pixel.r / 255.0;
        input[pixelIndex++] = pixel.g / 255.0;
        input[pixelIndex++] = pixel.b / 255.0;
      }
    }

    return input;
  }

  /// Libère les ressources
  @override
  void dispose() {
    stopDetection();
    _cameraController?.dispose();
    _interpreter?.close();
    super.dispose();
  }
}
