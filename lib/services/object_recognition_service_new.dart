import 'dart:math';
import 'package:flutter/material.dart';
import 'package:camera/camera.dart';
import 'tts_service.dart';

/// Classe pour représenter un objet détecté
class DetectedObject {
  final String className;
  final double confidence;
  final double x;
  final double y;
  final double width;
  final double height;

  DetectedObject({
    required this.className,
    required this.confidence,
    required this.x,
    required this.y,
    required this.width,
    required this.height,
  });

  /// Nom de la classe en français
  String get classNameFr {
    const translations = {
      'person': 'Personne',
      'chair': 'Chaise',
      'table': 'Table',
      'cup': 'Tasse',
      'bottle': 'Bouteille',
      'cell phone': 'Téléphone portable',
      'book': 'Livre',
      'laptop': 'Ordinateur portable',
      'car': 'Voiture',
      'bicycle': 'Vélo',
      'dog': 'Chien',
      'cat': 'Chat',
      'tv': 'Télévision',
      'mouse': 'Souris',
      'keyboard': 'Clavier',
      'clock': 'Horloge',
    };
    return translations[className.toLowerCase()] ?? className;
  }

  /// Pourcentage de confiance
  String get confidencePercentage {
    return '${(confidence * 100).toInt()}%';
  }

  @override
  String toString() {
    return 'DetectedObject(className: $className, confidence: ${(confidence * 100).toInt()}%)';
  }
}

/// Service de reconnaissance d'objets avec simulation temporaire
class ObjectRecognitionService extends ChangeNotifier {
  static final ObjectRecognitionService _instance =
      ObjectRecognitionService._internal();
  factory ObjectRecognitionService() => _instance;
  ObjectRecognitionService._internal();

  // Contrôleur de caméra
  CameraController? _cameraController;
  CameraController? get cameraController => _cameraController;

  // Simulation de détection d'objets
  List<String> _labels = [];
  final Random _random = Random();

  // Service TTS pour les annonces vocales
  final TtsService _ttsService = TtsService();

  // État du service
  bool _isInitialized = false;
  bool _isDetecting = false;
  bool _isModelLoaded = false;

  // Résultats de détection
  List<DetectedObject> _detectedObjects = [];
  List<DetectedObject> get detectedObjects => _detectedObjects;

  // Gestion des annonces vocales
  DateTime _lastAnnouncementTime = DateTime.now();
  String _lastAnnouncedObject = '';
  static const Duration _announcementCooldown = Duration(seconds: 3);

  // Getters pour l'état
  bool get isInitialized => _isInitialized;
  bool get isDetecting => _isDetecting;
  bool get isModelLoaded => _isModelLoaded;
  bool get isCameraReady => _cameraController?.value.isInitialized ?? false;

  /// Initialise le service de reconnaissance d'objets
  Future<bool> initialize() async {
    if (_isInitialized) return true;

    try {
      debugPrint('Initialisation du service de détection d\'objets...');

      // Charger le modèle (simulation)
      await _loadModel();

      // Initialiser la caméra
      await _initializeCamera();

      _isInitialized = true;
      notifyListeners();
      debugPrint('Service de détection d\'objets initialisé avec succès');
      return true;
    } catch (e) {
      debugPrint(
        'Erreur lors de l\'initialisation du service de reconnaissance: $e',
      );
      return false;
    }
  }

  /// Charge le modèle (simulation temporaire)
  Future<void> _loadModel() async {
    try {
      debugPrint('Chargement du modèle de détection d\'objets...');

      // Charger les labels COCO
      _labels = _getCocoLabels();

      _isModelLoaded = true;
      debugPrint('Modèle de détection chargé avec succès');
      debugPrint('Nombre de labels: ${_labels.length}');
      notifyListeners();
    } catch (e) {
      debugPrint('Erreur lors du chargement du modèle: $e');
      _isModelLoaded = false;
      notifyListeners();
      rethrow;
    }
  }

  /// Retourne les labels COCO (objets courants)
  List<String> _getCocoLabels() {
    return [
      'person',
      'chair',
      'table',
      'cup',
      'bottle',
      'cell phone',
      'book',
      'laptop',
      'car',
      'bicycle',
      'dog',
      'cat',
      'tv',
      'mouse',
      'keyboard',
      'clock',
    ];
  }

  /// Initialise la caméra
  Future<void> _initializeCamera() async {
    try {
      final cameras = await availableCameras();
      if (cameras.isEmpty) {
        throw Exception('Aucune caméra disponible');
      }

      // Utiliser la caméra arrière par défaut
      final camera = cameras.firstWhere(
        (camera) => camera.lensDirection == CameraLensDirection.back,
        orElse: () => cameras.first,
      );

      _cameraController = CameraController(
        camera,
        ResolutionPreset.medium,
        enableAudio: false,
        imageFormatGroup: ImageFormatGroup.yuv420,
      );

      await _cameraController!.initialize();
      notifyListeners();
      debugPrint('Caméra initialisée avec succès');
    } catch (e) {
      debugPrint('Erreur lors de l\'initialisation de la caméra: $e');
      rethrow;
    }
  }

  /// Démarre la détection en temps réel
  Future<void> startDetection() async {
    if (!_isInitialized || _isDetecting || !isCameraReady) return;

    _isDetecting = true;
    notifyListeners();

    try {
      await _cameraController!.startImageStream(_processImage);
      debugPrint('Détection en temps réel démarrée');
    } catch (e) {
      debugPrint('Erreur lors du démarrage de la détection: $e');
      _isDetecting = false;
      notifyListeners();
    }
  }

  /// Arrête la détection
  Future<void> stopDetection() async {
    if (!_isDetecting) return;

    try {
      await _cameraController?.stopImageStream();
      _isDetecting = false;
      _detectedObjects.clear();
      notifyListeners();
      debugPrint('Détection en temps réel arrêtée');
    } catch (e) {
      debugPrint('Erreur lors de l\'arrêt de la détection: $e');
    }
  }

  /// Traite une image de la caméra pour la détection (simulation temporaire)
  void _processImage(CameraImage cameraImage) async {
    if (!_isModelLoaded) return;

    try {
      // Simulation de détection d'objets
      final detections = await _simulateObjectDetection();

      // Mettre à jour les résultats
      _detectedObjects = detections;

      // Annoncer vocalement les objets détectés
      await _announceDetectedObjects(detections);

      notifyListeners();
    } catch (e) {
      debugPrint('Erreur lors du traitement de l\'image: $e');
    }
  }

  /// Simule la détection d'objets (temporaire)
  Future<List<DetectedObject>> _simulateObjectDetection() async {
    // Attendre un peu pour simuler le traitement
    await Future.delayed(const Duration(milliseconds: 2000));

    // Simuler la détection d'objets aléatoires
    final detections = <DetectedObject>[];

    if (_random.nextDouble() > 0.3) {
      // 70% de chance de détecter quelque chose
      final objectName = _labels[_random.nextInt(_labels.length)];
      final confidence =
          0.6 + _random.nextDouble() * 0.3; // 60-90% de confiance

      detections.add(
        DetectedObject(
          className: objectName,
          confidence: confidence,
          x: 0.3 + _random.nextDouble() * 0.4, // Position centrale
          y: 0.3 + _random.nextDouble() * 0.4,
          width: 0.2 + _random.nextDouble() * 0.3,
          height: 0.2 + _random.nextDouble() * 0.3,
        ),
      );
    }

    return detections;
  }

  /// Annonce vocalement les objets détectés
  Future<void> _announceDetectedObjects(List<DetectedObject> detections) async {
    if (detections.isEmpty) return;

    final now = DateTime.now();

    // Prendre l'objet avec la plus haute confiance
    final bestDetection = detections.first;
    final objectName = _translateToFrench(bestDetection.className);

    // Éviter les annonces trop fréquentes du même objet
    if (now.difference(_lastAnnouncementTime) > _announcementCooldown &&
        _lastAnnouncedObject != objectName) {
      try {
        await _ttsService.speak('Je vois $objectName');
        _lastAnnouncementTime = now;
        _lastAnnouncedObject = objectName;
        debugPrint('Annonce vocale: $objectName');
      } catch (e) {
        debugPrint('Erreur lors de l\'annonce vocale: $e');
      }
    }
  }

  /// Traduit les noms d'objets en français
  String _translateToFrench(String englishName) {
    const translations = {
      'person': 'une personne',
      'chair': 'une chaise',
      'table': 'une table',
      'cup': 'une tasse',
      'bottle': 'une bouteille',
      'cell phone': 'un téléphone portable',
      'book': 'un livre',
      'laptop': 'un ordinateur portable',
      'car': 'une voiture',
      'bicycle': 'un vélo',
      'dog': 'un chien',
      'cat': 'un chat',
      'tv': 'une télévision',
      'mouse': 'une souris',
      'keyboard': 'un clavier',
      'clock': 'une horloge',
    };

    return translations[englishName.toLowerCase()] ?? englishName;
  }

  /// Prend une photo et effectue la détection (simulation temporaire)
  Future<List<DetectedObject>> detectFromPhoto() async {
    if (!isCameraReady || !_isModelLoaded) {
      return [];
    }

    try {
      // Prendre une photo
      final image = await _cameraController!.takePicture();
      debugPrint('Photo prise pour la détection');

      // Simuler la détection d'objets
      return await _simulateObjectDetection();
    } catch (e) {
      debugPrint('Erreur lors de la détection sur photo: $e');
      return [];
    }
  }

  /// Libère les ressources
  @override
  void dispose() {
    stopDetection();
    _cameraController?.dispose();
    super.dispose();
  }
}
