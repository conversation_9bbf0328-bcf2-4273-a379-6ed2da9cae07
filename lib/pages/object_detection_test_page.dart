import 'dart:typed_data';
import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:image_picker/image_picker.dart';
import '../services/simple_object_detection_service.dart';
import '../models/detection_result.dart';

class ObjectDetectionTestPage extends StatefulWidget {
  const ObjectDetectionTestPage({super.key});

  @override
  State<ObjectDetectionTestPage> createState() => _ObjectDetectionTestPageState();
}

class _ObjectDetectionTestPageState extends State<ObjectDetectionTestPage> {
  final SimpleObjectDetectionService _detectionService = SimpleObjectDetectionService();
  final ImagePicker _imagePicker = ImagePicker();
  
  bool _isInitialized = false;
  bool _isDetecting = false;
  Uint8List? _currentImage;
  List<DetectionResult> _detectionResults = [];
  String _statusMessage = 'Initialisation...';

  @override
  void initState() {
    super.initState();
    _initializeService();
  }

  Future<void> _initializeService() async {
    setState(() {
      _statusMessage = 'Initialisation du service de détection...';
    });

    final success = await _detectionService.initialize();
    
    setState(() {
      _isInitialized = success;
      _statusMessage = success 
        ? 'Service initialisé ! Sélectionnez une image pour commencer.'
        : 'Erreur d\'initialisation du service.';
    });
  }

  Future<void> _pickImageFromGallery() async {
    if (!_isInitialized) return;

    try {
      final pickedFile = await _imagePicker.pickImage(source: ImageSource.gallery);
      if (pickedFile != null) {
        final imageBytes = await pickedFile.readAsBytes();
        await _runObjectDetection(imageBytes);
      }
    } catch (e) {
      _showSnackBar('Erreur lors de la sélection de l\'image: $e');
    }
  }

  Future<void> _pickImageFromCamera() async {
    if (!_isInitialized) return;

    try {
      final pickedFile = await _imagePicker.pickImage(source: ImageSource.camera);
      if (pickedFile != null) {
        final imageBytes = await pickedFile.readAsBytes();
        await _runObjectDetection(imageBytes);
      }
    } catch (e) {
      _showSnackBar('Erreur lors de la prise de photo: $e');
    }
  }

  Future<void> _runObjectDetection(Uint8List imageBytes) async {
    setState(() {
      _isDetecting = true;
      _statusMessage = 'Détection d\'objets en cours...';
      _currentImage = imageBytes;
      _detectionResults.clear();
    });

    try {
      // Exécuter la détection d'objets
      final results = await _detectionService.detectObjects(imageBytes);
      
      setState(() {
        _detectionResults = results;
        _isDetecting = false;
        _statusMessage = results.isEmpty 
          ? 'Aucun objet détecté.'
          : '${results.length} objet(s) détecté(s).';
      });
    } catch (e) {
      setState(() {
        _isDetecting = false;
        _statusMessage = 'Erreur lors de la détection: $e';
      });
    }
  }

  void _showSnackBar(String message) {
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(content: Text(message)),
    );
  }

  @override
  void dispose() {
    _detectionService.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('Test Détection d\'Objets'),
        backgroundColor: Colors.blue,
      ),
      body: Column(
        children: [
          // Barre de statut
          Container(
            width: double.infinity,
            padding: const EdgeInsets.all(16),
            color: _isInitialized ? Colors.green.withOpacity(0.1) : Colors.orange.withOpacity(0.1),
            child: Text(
              _statusMessage,
              textAlign: TextAlign.center,
              style: TextStyle(
                fontSize: 16,
                fontWeight: FontWeight.bold,
                color: _isInitialized ? Colors.green[700] : Colors.orange[700],
              ),
            ),
          ),

          // Boutons d'action
          Padding(
            padding: const EdgeInsets.all(16),
            child: Row(
              children: [
                Expanded(
                  child: ElevatedButton.icon(
                    onPressed: _isInitialized && !_isDetecting ? _pickImageFromGallery : null,
                    icon: const Icon(Icons.photo_library),
                    label: const Text('Galerie'),
                    style: ElevatedButton.styleFrom(
                      backgroundColor: Colors.blue,
                      foregroundColor: Colors.white,
                    ),
                  ),
                ),
                const SizedBox(width: 16),
                Expanded(
                  child: ElevatedButton.icon(
                    onPressed: _isInitialized && !_isDetecting ? _pickImageFromCamera : null,
                    icon: const Icon(Icons.camera_alt),
                    label: const Text('Caméra'),
                    style: ElevatedButton.styleFrom(
                      backgroundColor: Colors.green,
                      foregroundColor: Colors.white,
                    ),
                  ),
                ),
              ],
            ),
          ),

          // Zone d'affichage de l'image et des résultats
          Expanded(
            child: _currentImage != null
              ? Column(
                  children: [
                    // Image
                    Expanded(
                      flex: 2,
                      child: Container(
                        margin: const EdgeInsets.all(16),
                        decoration: BoxDecoration(
                          border: Border.all(color: Colors.grey),
                          borderRadius: BorderRadius.circular(8),
                        ),
                        child: ClipRRect(
                          borderRadius: BorderRadius.circular(8),
                          child: Image.memory(
                            _currentImage!,
                            fit: BoxFit.contain,
                            width: double.infinity,
                          ),
                        ),
                      ),
                    ),

                    // Résultats de détection
                    Expanded(
                      flex: 1,
                      child: Container(
                        width: double.infinity,
                        margin: const EdgeInsets.all(16),
                        padding: const EdgeInsets.all(16),
                        decoration: BoxDecoration(
                          color: Colors.grey[100],
                          borderRadius: BorderRadius.circular(8),
                          border: Border.all(color: Colors.grey[300]!),
                        ),
                        child: Column(
                          crossAxisAlignment: CrossAxisAlignment.start,
                          children: [
                            Text(
                              'Objets détectés:',
                              style: Theme.of(context).textTheme.titleMedium?.copyWith(
                                fontWeight: FontWeight.bold,
                              ),
                            ),
                            const SizedBox(height: 8),
                            Expanded(
                              child: _detectionResults.isEmpty
                                ? const Center(
                                    child: Text(
                                      'Aucun objet détecté',
                                      style: TextStyle(
                                        fontSize: 16,
                                        color: Colors.grey,
                                      ),
                                    ),
                                  )
                                : ListView.builder(
                                    itemCount: _detectionResults.length,
                                    itemBuilder: (context, index) {
                                      final result = _detectionResults[index];
                                      return Card(
                                        margin: const EdgeInsets.only(bottom: 8),
                                        child: ListTile(
                                          leading: CircleAvatar(
                                            backgroundColor: Colors.blue,
                                            child: Text('${index + 1}'),
                                          ),
                                          title: Text(
                                            result.label,
                                            style: const TextStyle(fontWeight: FontWeight.bold),
                                          ),
                                          subtitle: Text(
                                            'Confiance: ${(result.confidence * 100).toStringAsFixed(1)}%',
                                          ),
                                          trailing: Icon(
                                            Icons.check_circle,
                                            color: result.confidence > 0.7 ? Colors.green : Colors.orange,
                                          ),
                                        ),
                                      );
                                    },
                                  ),
                            ),
                          ],
                        ),
                      ),
                    ),
                  ],
                )
              : const Center(
                  child: Column(
                    mainAxisAlignment: MainAxisAlignment.center,
                    children: [
                      Icon(
                        Icons.image_search,
                        size: 64,
                        color: Colors.grey,
                      ),
                      SizedBox(height: 16),
                      Text(
                        'Sélectionnez une image pour commencer\nla détection d\'objets',
                        textAlign: TextAlign.center,
                        style: TextStyle(
                          fontSize: 16,
                          color: Colors.grey,
                        ),
                      ),
                    ],
                  ),
                ),
          ),

          // Indicateur de chargement
          if (_isDetecting)
            Container(
              padding: const EdgeInsets.all(16),
              child: const Row(
                mainAxisAlignment: MainAxisAlignment.center,
                children: [
                  CircularProgressIndicator(),
                  SizedBox(width: 16),
                  Text('Détection en cours...'),
                ],
              ),
            ),
        ],
      ),
    );
  }
}
