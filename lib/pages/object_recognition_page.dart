import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:provider/provider.dart';
import 'package:camera/camera.dart';
import 'package:voice_assistant/services/theme_service.dart';
import 'package:voice_assistant/services/object_recognition_service.dart';
import 'package:voice_assistant/services/tts_service.dart';
import 'package:permission_handler/permission_handler.dart';
import 'object_detection_test_page.dart';

class ObjectRecognitionPage extends StatefulWidget {
  const ObjectRecognitionPage({super.key});

  @override
  State<ObjectRecognitionPage> createState() => _ObjectRecognitionPageState();
}

class _ObjectRecognitionPageState extends State<ObjectRecognitionPage>
    with TickerProviderStateMixin {
  final ObjectRecognitionService _recognitionService =
      ObjectRecognitionService();
  final TtsService _ttsService = TtsService();

  late AnimationController _pulseController;
  late AnimationController _fadeController;
  late Animation<double> _pulseAnimation;
  late Animation<double> _fadeAnimation;

  bool _isInitializing = false;
  bool _hasPermission = false;
  String _statusMessage = 'Initialisation...';

  // Gestion des annonces TTS intelligentes
  List<String> _lastAnnouncedObjects = [];
  DateTime _lastAnnouncementTime = DateTime.now();
  bool _isSpeaking = false;
  bool _voiceAnnouncementsEnabled = true;

  @override
  void initState() {
    super.initState();
    _setupAnimations();
    _initializeService();
  }

  void _setupAnimations() {
    _pulseController = AnimationController(
      duration: const Duration(seconds: 2),
      vsync: this,
    );

    _fadeController = AnimationController(
      duration: const Duration(milliseconds: 800),
      vsync: this,
    );

    _pulseAnimation = Tween<double>(begin: 0.8, end: 1.2).animate(
      CurvedAnimation(parent: _pulseController, curve: Curves.easeInOut),
    );

    _fadeAnimation = Tween<double>(
      begin: 0.0,
      end: 1.0,
    ).animate(CurvedAnimation(parent: _fadeController, curve: Curves.easeIn));

    _pulseController.repeat(reverse: true);
    _fadeController.forward();
  }

  Future<void> _initializeService() async {
    setState(() {
      _isInitializing = true;
      _statusMessage = 'Vérification des permissions...';
    });

    // Vérifier les permissions
    final cameraPermission = await Permission.camera.request();

    if (cameraPermission.isGranted) {
      setState(() {
        _hasPermission = true;
        _statusMessage = 'Initialisation de la caméra...';
      });

      // Initialiser le service
      final success = await _recognitionService.initialize();

      if (success) {
        setState(() {
          _statusMessage = 'Prêt pour la reconnaissance !';
        });

        // Démarrer l'écoute des changements
        _recognitionService.addListener(_onRecognitionUpdate);
      } else {
        setState(() {
          _statusMessage = 'Erreur d\'initialisation';
        });
      }
    } else {
      setState(() {
        _hasPermission = false;
        _statusMessage = 'Permission caméra requise';
      });
    }

    setState(() {
      _isInitializing = false;
    });
  }

  void _onRecognitionUpdate() {
    if (mounted) {
      setState(() {});

      // Annoncer les nouveaux objets détectés
      _announceDetectedObjects();
    }
  }

  /// Annonce intelligemment les objets détectés
  Future<void> _announceDetectedObjects() async {
    if (_isSpeaking ||
        !_recognitionService.isDetecting ||
        !_voiceAnnouncementsEnabled)
      return;

    final currentObjects = _recognitionService.detectedObjects;
    if (currentObjects.isEmpty) return;

    // Vérifier si assez de temps s'est écoulé depuis la dernière annonce (5 secondes)
    final now = DateTime.now();
    if (now.difference(_lastAnnouncementTime).inSeconds < 5) return;

    // Obtenir les noms des objets actuels
    final currentObjectNames =
        currentObjects.map((obj) => obj.classNameFr).toSet().toList();

    // Vérifier s'il y a de nouveaux objets à annoncer
    final newObjects =
        currentObjectNames
            .where((name) => !_lastAnnouncedObjects.contains(name))
            .toList();

    if (newObjects.isNotEmpty) {
      _isSpeaking = true;
      _lastAnnouncementTime = now;

      String announcement;
      if (newObjects.length == 1) {
        announcement = 'Je vois: ${newObjects.first}';
      } else {
        announcement = 'Je vois: ${newObjects.join(', ')}';
      }

      try {
        await _ttsService.speak(announcement);

        // Mettre à jour la liste des objets annoncés
        _lastAnnouncedObjects = currentObjectNames;

        // Limiter la taille de la liste pour éviter une accumulation excessive
        if (_lastAnnouncedObjects.length > 10) {
          _lastAnnouncedObjects = _lastAnnouncedObjects.take(5).toList();
        }
      } catch (e) {
        debugPrint('Erreur lors de l\'annonce TTS: $e');
      } finally {
        _isSpeaking = false;
      }
    }
  }

  /// Bascule l'activation des annonces vocales
  void _toggleVoiceAnnouncements() {
    setState(() {
      _voiceAnnouncementsEnabled = !_voiceAnnouncementsEnabled;
    });

    // Annoncer le changement d'état
    final message =
        _voiceAnnouncementsEnabled
            ? 'Annonces vocales activées'
            : 'Annonces vocales désactivées';
    _ttsService.speak(message);
  }

  Future<void> _toggleDetection() async {
    if (_recognitionService.isDetecting) {
      await _recognitionService.stopDetection();
      await _ttsService.speak('Détection arrêtée');

      // Réinitialiser les annonces
      _lastAnnouncedObjects.clear();
      _isSpeaking = false;
    } else {
      await _recognitionService.startDetection();
      await _ttsService.speak(
        'Détection démarrée. Je vais vous annoncer les objets que je vois.',
      );

      // Réinitialiser les annonces pour une nouvelle session
      _lastAnnouncedObjects.clear();
      _lastAnnouncementTime = DateTime.now();
      _isSpeaking = false;
    }
  }

  Future<void> _takePhoto() async {
    try {
      // Arrêter temporairement les annonces automatiques
      _isSpeaking = true;

      HapticFeedback.mediumImpact();
      await _ttsService.speak('Analyse en cours...');

      final objects = await _recognitionService.detectFromPhoto();

      if (objects.isNotEmpty) {
        final objectNames = objects.map((obj) => obj.classNameFr).join(', ');

        String announcement;
        if (objects.length == 1) {
          announcement =
              'J\'ai détecté: ${objects.first.classNameFr} avec ${objects.first.confidencePercentage} de confiance';
        } else {
          announcement = 'J\'ai détecté ${objects.length} objets: $objectNames';
        }

        await _ttsService.speak(announcement);
        _showDetectionResults(objects);
      } else {
        await _ttsService.speak('Aucun objet détecté sur cette photo');
      }
    } catch (e) {
      await _ttsService.speak('Erreur lors de la prise de photo');
    } finally {
      // Réactiver les annonces automatiques après un délai
      Future.delayed(const Duration(seconds: 2), () {
        _isSpeaking = false;
      });
    }
  }

  void _showDetectionResults(List<DetectedObject> objects) {
    showModalBottomSheet(
      context: context,
      backgroundColor: Colors.transparent,
      builder: (context) => _buildResultsBottomSheet(objects),
    );
  }

  @override
  Widget build(BuildContext context) {
    final themeService = Provider.of<ThemeService>(context);
    final theme = themeService.getThemeData(context);

    return Scaffold(
      backgroundColor: Colors.black,
      appBar: AppBar(
        title: const Text(
          'Reconnaissance d\'Objets',
          style: TextStyle(fontWeight: FontWeight.w600, color: Colors.white),
        ),
        backgroundColor: Colors.transparent,
        elevation: 0,
        iconTheme: const IconThemeData(color: Colors.white),
        actions: [
          // Bouton pour tester la nouvelle détection TensorFlow Lite
          IconButton(
            icon: const Icon(Icons.science, color: Colors.white),
            onPressed: () {
              Navigator.push(
                context,
                MaterialPageRoute(
                  builder: (context) => const ObjectDetectionTestPage(),
                ),
              );
            },
            tooltip: 'Test TensorFlow Lite',
          ),
          if (_recognitionService.isInitialized) ...[
            IconButton(
              icon: Icon(
                _voiceAnnouncementsEnabled ? Icons.volume_up : Icons.volume_off,
                color: Colors.white,
              ),
              onPressed: _toggleVoiceAnnouncements,
              tooltip:
                  _voiceAnnouncementsEnabled
                      ? 'Désactiver les annonces vocales'
                      : 'Activer les annonces vocales',
            ),
            IconButton(
              icon: Icon(
                _recognitionService.isDetecting
                    ? Icons.pause_circle_filled
                    : Icons.play_circle_filled,
                color: Colors.white,
              ),
              onPressed: _toggleDetection,
              tooltip:
                  _recognitionService.isDetecting
                      ? 'Arrêter la détection'
                      : 'Démarrer la détection',
            ),
          ],
        ],
      ),
      body: _buildBody(theme),
      floatingActionButton: _buildFloatingActionButton(theme),
      floatingActionButtonLocation: FloatingActionButtonLocation.centerFloat,
    );
  }

  Widget _buildBody(ThemeData theme) {
    if (!_hasPermission) {
      return _buildPermissionView(theme);
    }

    if (_isInitializing || !_recognitionService.isInitialized) {
      return _buildLoadingView(theme);
    }

    if (!_recognitionService.isCameraReady) {
      return _buildErrorView(theme, 'Caméra non disponible');
    }

    return Stack(
      children: [
        // Aperçu de la caméra
        _buildCameraPreview(),

        // Overlay avec les détections
        if (_recognitionService.isDetecting) _buildDetectionOverlay(),

        // Informations en haut
        _buildTopInfo(theme),

        // Statistiques en bas
        _buildBottomStats(theme),
      ],
    );
  }

  Widget _buildPermissionView(ThemeData theme) {
    return Center(
      child: FadeTransition(
        opacity: _fadeAnimation,
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            AnimatedBuilder(
              animation: _pulseAnimation,
              builder: (context, child) {
                return Transform.scale(
                  scale: _pulseAnimation.value,
                  child: Container(
                    padding: const EdgeInsets.all(24),
                    decoration: BoxDecoration(
                      color: Colors.red.withOpacity(0.1),
                      shape: BoxShape.circle,
                      border: Border.all(
                        color: Colors.red.withOpacity(0.3),
                        width: 2,
                      ),
                    ),
                    child: const Icon(
                      Icons.camera_alt_outlined,
                      size: 64,
                      color: Colors.red,
                    ),
                  ),
                );
              },
            ),
            const SizedBox(height: 32),
            const Text(
              'Permission Caméra Requise',
              style: TextStyle(
                fontSize: 24,
                fontWeight: FontWeight.bold,
                color: Colors.white,
              ),
            ),
            const SizedBox(height: 16),
            const Padding(
              padding: EdgeInsets.symmetric(horizontal: 40),
              child: Text(
                'Pour utiliser la reconnaissance d\'objets, nous avons besoin d\'accéder à votre caméra.',
                textAlign: TextAlign.center,
                style: TextStyle(fontSize: 16, color: Colors.white70),
              ),
            ),
            const SizedBox(height: 32),
            ElevatedButton.icon(
              onPressed: _initializeService,
              icon: const Icon(Icons.camera_alt),
              label: const Text('Autoriser la Caméra'),
              style: ElevatedButton.styleFrom(
                backgroundColor: theme.primaryColor,
                foregroundColor: Colors.white,
                padding: const EdgeInsets.symmetric(
                  horizontal: 24,
                  vertical: 12,
                ),
                shape: RoundedRectangleBorder(
                  borderRadius: BorderRadius.circular(25),
                ),
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildLoadingView(ThemeData theme) {
    return Center(
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          AnimatedBuilder(
            animation: _pulseAnimation,
            builder: (context, child) {
              return Transform.scale(
                scale: _pulseAnimation.value,
                child: Container(
                  padding: const EdgeInsets.all(24),
                  decoration: BoxDecoration(
                    color: theme.primaryColor.withOpacity(0.1),
                    shape: BoxShape.circle,
                    border: Border.all(
                      color: theme.primaryColor.withOpacity(0.3),
                      width: 2,
                    ),
                  ),
                  child: Icon(
                    Icons.smart_toy_outlined,
                    size: 64,
                    color: theme.primaryColor,
                  ),
                ),
              );
            },
          ),
          const SizedBox(height: 32),
          const Text(
            'Initialisation...',
            style: TextStyle(
              fontSize: 24,
              fontWeight: FontWeight.bold,
              color: Colors.white,
            ),
          ),
          const SizedBox(height: 16),
          Text(
            _statusMessage,
            textAlign: TextAlign.center,
            style: const TextStyle(fontSize: 16, color: Colors.white70),
          ),
          const SizedBox(height: 32),
          const CircularProgressIndicator(
            valueColor: AlwaysStoppedAnimation<Color>(Colors.white),
          ),
        ],
      ),
    );
  }

  Widget _buildErrorView(ThemeData theme, String message) {
    return Center(
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          const Icon(Icons.error_outline, size: 64, color: Colors.red),
          const SizedBox(height: 16),
          const Text(
            'Erreur',
            style: TextStyle(
              fontSize: 24,
              fontWeight: FontWeight.bold,
              color: Colors.white,
            ),
          ),
          const SizedBox(height: 8),
          Text(
            message,
            textAlign: TextAlign.center,
            style: const TextStyle(fontSize: 16, color: Colors.white70),
          ),
          const SizedBox(height: 24),
          ElevatedButton.icon(
            onPressed: _initializeService,
            icon: const Icon(Icons.refresh),
            label: const Text('Réessayer'),
            style: ElevatedButton.styleFrom(
              backgroundColor: theme.primaryColor,
              foregroundColor: Colors.white,
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildCameraPreview() {
    if (_recognitionService.cameraController == null) {
      return const Center(
        child: CircularProgressIndicator(color: Colors.white),
      );
    }

    return ClipRRect(
      borderRadius: BorderRadius.circular(0),
      child: CameraPreview(_recognitionService.cameraController!),
    );
  }

  Widget _buildDetectionOverlay() {
    return CustomPaint(
      painter: DetectionPainter(_recognitionService.detectedObjects),
      size: Size.infinite,
    );
  }

  Widget _buildTopInfo(ThemeData theme) {
    return Positioned(
      top: 0,
      left: 0,
      right: 0,
      child: Container(
        padding: const EdgeInsets.all(16),
        decoration: BoxDecoration(
          gradient: LinearGradient(
            begin: Alignment.topCenter,
            end: Alignment.bottomCenter,
            colors: [Colors.black.withValues(alpha: 0.7), Colors.transparent],
          ),
        ),
        child: SafeArea(
          child: Column(
            children: [
              if (_recognitionService.isDetecting)
                Container(
                  padding: const EdgeInsets.symmetric(
                    horizontal: 12,
                    vertical: 6,
                  ),
                  decoration: BoxDecoration(
                    color: Colors.green.withValues(alpha: 0.2),
                    borderRadius: BorderRadius.circular(20),
                    border: Border.all(
                      color: Colors.green.withValues(alpha: 0.5),
                      width: 1,
                    ),
                  ),
                  child: Row(
                    mainAxisSize: MainAxisSize.min,
                    children: [
                      Container(
                        width: 8,
                        height: 8,
                        decoration: const BoxDecoration(
                          color: Colors.green,
                          shape: BoxShape.circle,
                        ),
                      ),
                      const SizedBox(width: 8),
                      const Text(
                        'Détection Active',
                        style: TextStyle(
                          color: Colors.white,
                          fontSize: 12,
                          fontWeight: FontWeight.w500,
                        ),
                      ),
                    ],
                  ),
                ),
            ],
          ),
        ),
      ),
    );
  }

  Widget _buildBottomStats(ThemeData theme) {
    final detectedObjects = _recognitionService.detectedObjects;

    return Positioned(
      bottom: 100,
      left: 16,
      right: 16,
      child: Container(
        padding: const EdgeInsets.all(16),
        decoration: BoxDecoration(
          color: Colors.black.withValues(alpha: 0.7),
          borderRadius: BorderRadius.circular(16),
          border: Border.all(
            color: Colors.white.withValues(alpha: 0.2),
            width: 1,
          ),
        ),
        child: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            Row(
              mainAxisAlignment: MainAxisAlignment.spaceBetween,
              children: [
                Text(
                  'Objets détectés',
                  style: TextStyle(
                    color: Colors.white,
                    fontSize: 16,
                    fontWeight: FontWeight.w600,
                  ),
                ),
                Container(
                  padding: const EdgeInsets.symmetric(
                    horizontal: 8,
                    vertical: 4,
                  ),
                  decoration: BoxDecoration(
                    color: theme.primaryColor.withValues(alpha: 0.2),
                    borderRadius: BorderRadius.circular(12),
                  ),
                  child: Text(
                    '${detectedObjects.length}',
                    style: TextStyle(
                      color: theme.primaryColor,
                      fontSize: 14,
                      fontWeight: FontWeight.bold,
                    ),
                  ),
                ),
              ],
            ),
            if (detectedObjects.isNotEmpty) ...[
              const SizedBox(height: 12),
              SizedBox(
                height: 60,
                child: ListView.builder(
                  scrollDirection: Axis.horizontal,
                  itemCount: detectedObjects.length,
                  itemBuilder: (context, index) {
                    final obj = detectedObjects[index];
                    return Container(
                      margin: const EdgeInsets.only(right: 8),
                      padding: const EdgeInsets.symmetric(
                        horizontal: 12,
                        vertical: 8,
                      ),
                      decoration: BoxDecoration(
                        color: Colors.white.withValues(alpha: 0.1),
                        borderRadius: BorderRadius.circular(12),
                        border: Border.all(
                          color: Colors.white.withValues(alpha: 0.3),
                          width: 1,
                        ),
                      ),
                      child: Column(
                        mainAxisSize: MainAxisSize.min,
                        children: [
                          Text(
                            obj.classNameFr,
                            style: const TextStyle(
                              color: Colors.white,
                              fontSize: 12,
                              fontWeight: FontWeight.w500,
                            ),
                          ),
                          const SizedBox(height: 4),
                          Text(
                            obj.confidencePercentage,
                            style: TextStyle(
                              color: Colors.white.withValues(alpha: 0.7),
                              fontSize: 10,
                            ),
                          ),
                        ],
                      ),
                    );
                  },
                ),
              ),
            ],
          ],
        ),
      ),
    );
  }

  Widget _buildFloatingActionButton(ThemeData theme) {
    if (!_recognitionService.isInitialized ||
        !_recognitionService.isCameraReady) {
      return const SizedBox.shrink();
    }

    return Container(
      margin: const EdgeInsets.only(bottom: 20),
      child: FloatingActionButton.large(
        onPressed: _takePhoto,
        backgroundColor: theme.primaryColor,
        foregroundColor: Colors.white,
        elevation: 8,
        child: const Icon(Icons.camera_alt, size: 32),
      ),
    );
  }

  Widget _buildResultsBottomSheet(List<DetectedObject> objects) {
    return Container(
      margin: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(20),
      ),
      child: Column(
        mainAxisSize: MainAxisSize.min,
        children: [
          Container(
            padding: const EdgeInsets.all(20),
            decoration: BoxDecoration(
              color: Colors.grey[100],
              borderRadius: const BorderRadius.only(
                topLeft: Radius.circular(20),
                topRight: Radius.circular(20),
              ),
            ),
            child: Row(
              children: [
                Icon(Icons.smart_toy, color: Colors.grey[600]),
                const SizedBox(width: 12),
                const Text(
                  'Objets Détectés',
                  style: TextStyle(fontSize: 18, fontWeight: FontWeight.bold),
                ),
                const Spacer(),
                IconButton(
                  onPressed: () => Navigator.pop(context),
                  icon: const Icon(Icons.close),
                ),
              ],
            ),
          ),
          Flexible(
            child: ListView.builder(
              shrinkWrap: true,
              itemCount: objects.length,
              itemBuilder: (context, index) {
                final obj = objects[index];
                return ListTile(
                  leading: CircleAvatar(
                    backgroundColor: Colors.blue[100],
                    child: Text(
                      '${index + 1}',
                      style: TextStyle(
                        color: Colors.blue[800],
                        fontWeight: FontWeight.bold,
                      ),
                    ),
                  ),
                  title: Text(
                    obj.classNameFr,
                    style: const TextStyle(fontWeight: FontWeight.w600),
                  ),
                  subtitle: Text('Confiance: ${obj.confidencePercentage}'),
                  trailing: Icon(Icons.check_circle, color: Colors.green[600]),
                );
              },
            ),
          ),
          const SizedBox(height: 20),
        ],
      ),
    );
  }

  @override
  void dispose() {
    // Arrêter les annonces TTS
    _isSpeaking = false;
    _lastAnnouncedObjects.clear();

    // Arrêter la détection si active
    if (_recognitionService.isDetecting) {
      _recognitionService.stopDetection();
    }

    _pulseController.dispose();
    _fadeController.dispose();
    _recognitionService.removeListener(_onRecognitionUpdate);
    _recognitionService.dispose();
    super.dispose();
  }
}

/// Painter personnalisé pour dessiner les boîtes de détection
class DetectionPainter extends CustomPainter {
  final List<DetectedObject> detectedObjects;

  DetectionPainter(this.detectedObjects);

  @override
  void paint(Canvas canvas, Size size) {
    if (detectedObjects.isEmpty) return;

    final paint =
        Paint()
          ..style = PaintingStyle.stroke
          ..strokeWidth = 3.0;

    final textPainter = TextPainter(textDirection: TextDirection.ltr);

    for (int i = 0; i < detectedObjects.length; i++) {
      final obj = detectedObjects[i];

      // Couleur différente pour chaque objet
      final colors = [
        Colors.red,
        Colors.blue,
        Colors.green,
        Colors.orange,
        Colors.purple,
        Colors.cyan,
        Colors.pink,
        Colors.yellow,
      ];
      final color = colors[i % colors.length];

      paint.color = color;

      // Calculer les coordonnées de la boîte
      final left = obj.x * size.width;
      final top = obj.y * size.height;
      final right = (obj.x + obj.width) * size.width;
      final bottom = (obj.y + obj.height) * size.height;

      // Dessiner la boîte de détection
      final rect = Rect.fromLTRB(left, top, right, bottom);
      canvas.drawRect(rect, paint);

      // Dessiner le fond du label
      final labelText = '${obj.classNameFr} ${obj.confidencePercentage}';
      textPainter.text = TextSpan(
        text: labelText,
        style: const TextStyle(
          color: Colors.white,
          fontSize: 14,
          fontWeight: FontWeight.bold,
        ),
      );
      textPainter.layout();

      final labelRect = Rect.fromLTWH(
        left,
        top - textPainter.height - 8,
        textPainter.width + 16,
        textPainter.height + 8,
      );

      final labelPaint = Paint()..color = color.withValues(alpha: 0.8);
      canvas.drawRect(labelRect, labelPaint);

      // Dessiner le texte du label
      textPainter.paint(canvas, Offset(left + 8, top - textPainter.height - 4));
    }
  }

  @override
  bool shouldRepaint(DetectionPainter oldDelegate) {
    return detectedObjects != oldDelegate.detectedObjects;
  }
}
