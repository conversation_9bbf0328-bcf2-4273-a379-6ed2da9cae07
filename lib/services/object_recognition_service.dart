import 'dart:io';
import 'dart:typed_data';
import 'dart:ui' as ui;
import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:camera/camera.dart';
import 'package:tflite_flutter/tflite_flutter.dart';
import 'package:image/image.dart' as img;
import '../models/detection_result.dart';

/// Service de reconnaissance d'objets utilisant TensorFlow Lite
class ObjectRecognitionService extends ChangeNotifier {
  static final ObjectRecognitionService _instance =
      ObjectRecognitionService._internal();
  factory ObjectRecognitionService() => _instance;
  ObjectRecognitionService._internal();

  // Contrôleur de caméra
  CameraController? _cameraController;
  CameraController? get cameraController => _cameraController;

  // Modèle TensorFlow Lite
  Interpreter? _interpreter;
  List<String> _labels = [];

  // État du service
  bool _isInitialized = false;
  bool _isDetecting = false;
  bool _isModelLoaded = false;

  // Résultats de détection
  List<DetectedObject> _detectedObjects = [];
  List<DetectedObject> get detectedObjects => _detectedObjects;

  // Configuration du modèle
  static const int _inputSize = 640;
  static const double _threshold = 0.5;
  static const int _numDetections = 25200;

  // Getters pour l'état
  bool get isInitialized => _isInitialized;
  bool get isDetecting => _isDetecting;
  bool get isModelLoaded => _isModelLoaded;
  bool get isCameraReady => _cameraController?.value.isInitialized ?? false;

  /// Initialise le service de reconnaissance d'objets
  Future<bool> initialize() async {
    if (_isInitialized) return true;

    try {
      debugPrint('Initialisation du service TensorFlow Lite...');

      // Charger le modèle TensorFlow Lite
      await _loadTFLiteModel();

      // Initialiser la caméra
      await _initializeCamera();

      _isInitialized = true;
      notifyListeners();
      debugPrint('Service TensorFlow Lite initialisé avec succès');
      return true;
    } catch (e) {
      debugPrint(
        'Erreur lors de l\'initialisation du service de reconnaissance: $e',
      );
      return false;
    }
  }

  /// Charge le modèle TensorFlow Lite
  Future<void> _loadTFLiteModel() async {
    try {
      debugPrint('Chargement du modèle TensorFlow Lite...');

      // Charger le modèle depuis les assets
      _interpreter = await Interpreter.fromAsset('assets/models/model.tflite');

      // Charger les labels COCO
      _labels = await _loadCocoLabels();

      _isModelLoaded = true;
      debugPrint('Modèle TensorFlow Lite chargé avec succès');
      debugPrint('Nombre de labels: ${_labels.length}');
      notifyListeners();
    } catch (e) {
      debugPrint('Erreur lors du chargement du modèle TensorFlow Lite: $e');
      _isModelLoaded = false;
      notifyListeners();
      rethrow;
    }
  }

  /// Charge les labels COCO (80 classes)
  Future<List<String>> _loadCocoLabels() async {
    return [
      'person',
      'bicycle',
      'car',
      'motorcycle',
      'airplane',
      'bus',
      'train',
      'truck',
      'boat',
      'traffic light',
      'fire hydrant',
      'stop sign',
      'parking meter',
      'bench',
      'bird',
      'cat',
      'dog',
      'horse',
      'sheep',
      'cow',
      'elephant',
      'bear',
      'zebra',
      'giraffe',
      'backpack',
      'umbrella',
      'handbag',
      'tie',
      'suitcase',
      'frisbee',
      'skis',
      'snowboard',
      'sports ball',
      'kite',
      'baseball bat',
      'baseball glove',
      'skateboard',
      'surfboard',
      'tennis racket',
      'bottle',
      'wine glass',
      'cup',
      'fork',
      'knife',
      'spoon',
      'bowl',
      'banana',
      'apple',
      'sandwich',
      'orange',
      'broccoli',
      'carrot',
      'hot dog',
      'pizza',
      'donut',
      'cake',
      'chair',
      'couch',
      'potted plant',
      'bed',
      'dining table',
      'toilet',
      'tv',
      'laptop',
      'mouse',
      'remote',
      'keyboard',
      'cell phone',
      'microwave',
      'oven',
      'toaster',
      'sink',
      'refrigerator',
      'book',
      'clock',
      'vase',
      'scissors',
      'teddy bear',
      'hair drier',
      'toothbrush',
    ];
  }

  /// Initialise la caméra
  Future<void> _initializeCamera() async {
    try {
      final cameras = await availableCameras();
      if (cameras.isEmpty) {
        throw Exception('Aucune caméra disponible');
      }

      // Utiliser la caméra arrière par défaut
      final camera = cameras.firstWhere(
        (camera) => camera.lensDirection == CameraLensDirection.back,
        orElse: () => cameras.first,
      );

      _cameraController = CameraController(
        camera,
        ResolutionPreset.medium,
        enableAudio: false,
        imageFormatGroup: ImageFormatGroup.yuv420,
      );

      await _cameraController!.initialize();
      notifyListeners();
      debugPrint('Caméra initialisée avec succès');
    } catch (e) {
      debugPrint('Erreur lors de l\'initialisation de la caméra: $e');
      rethrow;
    }
  }

  /// Démarre la détection en temps réel
  Future<void> startDetection() async {
    if (!_isInitialized || _isDetecting || !isCameraReady) return;

    _isDetecting = true;
    notifyListeners();

    try {
      await _cameraController!.startImageStream(_processImage);
      debugPrint('Détection en temps réel démarrée');
    } catch (e) {
      debugPrint('Erreur lors du démarrage de la détection: $e');
      _isDetecting = false;
      notifyListeners();
    }
  }

  /// Arrête la détection
  Future<void> stopDetection() async {
    if (!_isDetecting) return;

    try {
      await _cameraController?.stopImageStream();
      _isDetecting = false;
      _detectedObjects.clear();
      notifyListeners();
      debugPrint('Détection en temps réel arrêtée');
    } catch (e) {
      debugPrint('Erreur lors de l\'arrêt de la détection: $e');
    }
  }

  /// Traite une image de la caméra pour la détection
  void _processImage(CameraImage cameraImage) async {
    if (!_isModelLoaded || _interpreter == null) return;

    try {
      // Convertir l'image de la caméra en format utilisable
      final inputImage = _preprocessCameraImage(cameraImage);

      // Exécuter l'inférence
      final detections = await _runInference(inputImage);

      // Mettre à jour les résultats
      _detectedObjects = detections;
      notifyListeners();
    } catch (e) {
      debugPrint('Erreur lors du traitement de l\'image: $e');
    }
  }

  /// Préprocesse l'image de la caméra pour le modèle
  Float32List _preprocessCameraImage(CameraImage cameraImage) {
    // Convertir YUV420 en RGB et redimensionner
    final rgbImage = _convertYUV420ToRGB(cameraImage);
    final resizedImage = img.copyResize(
      rgbImage,
      width: _inputSize,
      height: _inputSize,
    );

    // Normaliser les pixels (0-1)
    final input = Float32List(_inputSize * _inputSize * 3);
    int pixelIndex = 0;

    for (int y = 0; y < _inputSize; y++) {
      for (int x = 0; x < _inputSize; x++) {
        final pixel = resizedImage.getPixel(x, y);
        input[pixelIndex++] = pixel.r / 255.0;
        input[pixelIndex++] = pixel.g / 255.0;
        input[pixelIndex++] = pixel.b / 255.0;
      }
    }

    return input;
  }

  /// Convertit YUV420 en RGB
  img.Image _convertYUV420ToRGB(CameraImage cameraImage) {
    final width = cameraImage.width;
    final height = cameraImage.height;

    final yPlane = cameraImage.planes[0];
    final uPlane = cameraImage.planes[1];
    final vPlane = cameraImage.planes[2];

    final image = img.Image(width: width, height: height);

    for (int y = 0; y < height; y++) {
      for (int x = 0; x < width; x++) {
        final yIndex = y * yPlane.bytesPerRow + x;
        final uvIndex = (y ~/ 2) * uPlane.bytesPerRow + (x ~/ 2);

        if (yIndex < yPlane.bytes.length && uvIndex < uPlane.bytes.length) {
          final yValue = yPlane.bytes[yIndex];
          final uValue = uPlane.bytes[uvIndex];
          final vValue = vPlane.bytes[uvIndex];

          // Conversion YUV vers RGB
          final r = (yValue + 1.402 * (vValue - 128)).clamp(0, 255).toInt();
          final g =
              (yValue - 0.344 * (uValue - 128) - 0.714 * (vValue - 128))
                  .clamp(0, 255)
                  .toInt();
          final b = (yValue + 1.772 * (uValue - 128)).clamp(0, 255).toInt();

          image.setPixel(x, y, img.ColorRgb8(r, g, b));
        }
      }
    }

    return image;
  }

  /// Exécute l'inférence sur l'image
  Future<List<DetectedObject>> _runInference(Float32List inputImage) async {
    if (_interpreter == null) return [];

    try {
      // Préparer les tenseurs d'entrée et de sortie
      final input = inputImage.reshape([1, _inputSize, _inputSize, 3]);
      final outputs = <int, Object>{};

      // Configuration de sortie basique (à ajuster selon le modèle)
      outputs[0] = Float32List(1 * _numDetections * 85); // détections

      // Exécuter l'inférence
      _interpreter!.runForMultipleInputs([input], outputs);

      // Post-traiter les résultats
      return _postProcessResults(outputs[0] as Float32List);
    } catch (e) {
      debugPrint('Erreur lors de l\'inférence: $e');
      return [];
    }
  }

  /// Post-traite les résultats de l'inférence
  List<DetectedObject> _postProcessResults(Float32List rawOutput) {
    final detections = <DetectedObject>[];

    try {
      // Traitement simplifié - à adapter selon le format de sortie du modèle
      for (int i = 0; i < _numDetections; i++) {
        final offset = i * 85;

        if (offset + 84 < rawOutput.length) {
          final confidence = rawOutput[offset + 4];

          if (confidence > _threshold) {
            // Trouver la classe avec la plus haute probabilité
            double maxClassProb = 0;
            int maxClassIndex = 0;

            for (int j = 0; j < 80; j++) {
              final classProb = rawOutput[offset + 5 + j];
              if (classProb > maxClassProb) {
                maxClassProb = classProb;
                maxClassIndex = j;
              }
            }

            final finalConfidence = confidence * maxClassProb;

            if (finalConfidence > _threshold &&
                maxClassIndex < _labels.length) {
              // Extraire les coordonnées de la boîte englobante
              final centerX = rawOutput[offset];
              final centerY = rawOutput[offset + 1];
              final width = rawOutput[offset + 2];
              final height = rawOutput[offset + 3];

              detections.add(
                DetectedObject(
                  className: _labels[maxClassIndex],
                  confidence: finalConfidence,
                  x: centerX,
                  y: centerY,
                  width: width,
                  height: height,
                ),
              );
            }
          }
        }
      }
    } catch (e) {
      debugPrint('Erreur lors du post-traitement: $e');
    }

    return detections;
  }

  /// Prend une photo et effectue la détection
  Future<List<DetectedObject>> detectFromPhoto() async {
    if (!isCameraReady || !_isModelLoaded || _interpreter == null) {
      return [];
    }

    try {
      // Prendre une photo
      final image = await _cameraController!.takePicture();

      // Lire et traiter l'image
      final imageBytes = await image.readAsBytes();
      final decodedImage = img.decodeImage(imageBytes);

      if (decodedImage != null) {
        // Préprocesser l'image
        final resizedImage = img.copyResize(
          decodedImage,
          width: _inputSize,
          height: _inputSize,
        );
        final inputData = _imageToFloat32List(resizedImage);

        // Exécuter l'inférence
        final detections = await _runInference(inputData);
        return detections;
      }

      return [];
    } catch (e) {
      debugPrint('Erreur lors de la détection sur photo: $e');
      return [];
    }
  }

  /// Convertit une image en Float32List
  Float32List _imageToFloat32List(img.Image image) {
    final input = Float32List(_inputSize * _inputSize * 3);
    int pixelIndex = 0;

    for (int y = 0; y < _inputSize; y++) {
      for (int x = 0; x < _inputSize; x++) {
        final pixel = image.getPixel(x, y);
        input[pixelIndex++] = pixel.r / 255.0;
        input[pixelIndex++] = pixel.g / 255.0;
        input[pixelIndex++] = pixel.b / 255.0;
      }
    }

    return input;
  }

  /// Libère les ressources
  @override
  void dispose() {
    stopDetection();
    _cameraController?.dispose();
    _interpreter?.close();
    super.dispose();
  }
}

/// Classe représentant un objet détecté
class DetectedObject {
  final String className;
  final double confidence;
  final double x;
  final double y;
  final double width;
  final double height;

  DetectedObject({
    required this.className,
    required this.confidence,
    required this.x,
    required this.y,
    required this.width,
    required this.height,
  });

  factory DetectedObject.fromMap(Map<String, dynamic> map) {
    return DetectedObject(
      className: map['class'] ?? 'Inconnu',
      confidence: (map['confidence'] ?? 0.0).toDouble(),
      x: (map['x'] ?? 0.0).toDouble(),
      y: (map['y'] ?? 0.0).toDouble(),
      width: (map['width'] ?? 0.0).toDouble(),
      height: (map['height'] ?? 0.0).toDouble(),
    );
  }

  /// Retourne le pourcentage de confiance formaté
  String get confidencePercentage =>
      '${(confidence * 100).toStringAsFixed(1)}%';

  /// Retourne le nom de classe traduit en français
  String get classNameFr {
    const translations = {
      'person': 'Personne',
      'car': 'Voiture',
      'dog': 'Chien',
      'cat': 'Chat',
      'chair': 'Chaise',
      'table': 'Table',
      'phone': 'Téléphone',
      'laptop': 'Ordinateur portable',
      'book': 'Livre',
      'bottle': 'Bouteille',
      'cup': 'Tasse',
      'knife': 'Couteau',
      'spoon': 'Cuillère',
      'fork': 'Fourchette',
      'bowl': 'Bol',
      'banana': 'Banane',
      'apple': 'Pomme',
      'orange': 'Orange',
      'clock': 'Horloge',
      'tv': 'Télévision',
      'keyboard': 'Clavier',
      'mouse': 'Souris',
    };

    return translations[className.toLowerCase()] ?? className;
  }
}
