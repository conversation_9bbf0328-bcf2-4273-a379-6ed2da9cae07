import 'dart:ui';

/// Classe pour représenter un résultat de détection d'objet
class DetectionResult {
  final Rect boundingBox;
  final String text;
  final String label;
  final double confidence;

  DetectionResult({
    required this.boundingBox,
    required this.text,
    required this.label,
    required this.confidence,
  });

  @override
  String toString() {
    return 'DetectionResult(label: $label, confidence: ${(confidence * 100).toInt()}%, boundingBox: $boundingBox)';
  }
}

/// Classe pour représenter un objet détecté (compatible avec l'ancienne implémentation)
class DetectedObject {
  final String label;
  final double confidence;
  final Rect boundingBox;

  DetectedObject({
    required this.label,
    required this.confidence,
    required this.boundingBox,
  });

  @override
  String toString() {
    return 'DetectedObject(label: $label, confidence: ${(confidence * 100).toInt()}%)';
  }
}
