import 'dart:math';
import 'package:flutter/material.dart';
import 'package:camera/camera.dart';
import 'package:tflite/tflite.dart';
import 'tts_service.dart';

/// Classe pour représenter un objet détecté
class DetectedObject {
  final String className;
  final double confidence;
  final double x;
  final double y;
  final double width;
  final double height;

  DetectedObject({
    required this.className,
    required this.confidence,
    required this.x,
    required this.y,
    required this.width,
    required this.height,
  });

  /// Nom de la classe en français
  String get classNameFr {
    const translations = {
      'person': 'Personne',
      'chair': 'Chaise',
      'table': 'Table',
      'cup': 'Tasse',
      'bottle': 'Bouteille',
      'cell phone': 'Téléphone portable',
      'book': 'Livre',
      'laptop': 'Ordinateur portable',
      'car': 'Voiture',
      'bicycle': 'Vélo',
      'dog': 'Chien',
      'cat': 'Chat',
      'tv': 'Télévision',
      'mouse': 'Souris',
      'keyboard': 'Clavier',
      'clock': 'Horloge',
    };
    return translations[className.toLowerCase()] ?? className;
  }

  /// Pourcentage de confiance
  String get confidencePercentage {
    return '${(confidence * 100).toInt()}%';
  }

  @override
  String toString() {
    return 'DetectedObject(className: $className, confidence: ${(confidence * 100).toInt()}%)';
  }
}

/// Service de reconnaissance d'objets utilisant TFLite
class ObjectRecognitionService extends ChangeNotifier {
  static final ObjectRecognitionService _instance =
      ObjectRecognitionService._internal();
  factory ObjectRecognitionService() => _instance;
  ObjectRecognitionService._internal();

  // Contrôleur de caméra
  CameraController? _cameraController;
  CameraController? get cameraController => _cameraController;

  // Service TTS pour les annonces vocales
  final TtsService _ttsService = TtsService();

  // État du service
  bool _isInitialized = false;
  bool _isDetecting = false;
  bool _isModelLoaded = false;

  // Résultats de détection
  List<DetectedObject> _detectedObjects = [];
  List<DetectedObject> get detectedObjects => _detectedObjects;

  // Gestion des annonces vocales
  DateTime _lastAnnouncementTime = DateTime.now();
  String _lastAnnouncedObject = '';
  static const Duration _announcementCooldown = Duration(seconds: 3);

  // Getters pour l'état
  bool get isInitialized => _isInitialized;
  bool get isDetecting => _isDetecting;
  bool get isModelLoaded => _isModelLoaded;
  bool get isCameraReady => _cameraController?.value.isInitialized ?? false;

  /// Initialise le service de reconnaissance d'objets
  Future<bool> initialize() async {
    if (_isInitialized) return true;

    try {
      debugPrint('Initialisation du service TFLite...');

      // Charger le modèle TFLite
      await _loadTFLiteModel();

      // Initialiser la caméra
      await _initializeCamera();

      _isInitialized = true;
      notifyListeners();
      debugPrint('Service TFLite initialisé avec succès');
      return true;
    } catch (e) {
      debugPrint('Erreur lors de l\'initialisation du service TFLite: $e');
      return false;
    }
  }

  /// Charge le modèle TFLite
  Future<void> _loadTFLiteModel() async {
    try {
      debugPrint('Chargement du modèle TFLite...');

      // Charger le modèle depuis les assets
      String? result = await Tflite.loadModel(
        model: "assets/models/model.tflite",
        labels: "assets/models/labels.txt",
        numThreads: 1, // Utiliser 1 thread pour éviter les problèmes
        isAsset: true,
        useGpuDelegate: false, // Désactiver GPU pour éviter les problèmes
      );

      if (result != null) {
        _isModelLoaded = true;
        debugPrint('Modèle TFLite chargé avec succès: $result');
      } else {
        throw Exception('Échec du chargement du modèle TFLite');
      }

      notifyListeners();
    } catch (e) {
      debugPrint('Erreur lors du chargement du modèle TFLite: $e');
      _isModelLoaded = false;
      notifyListeners();
      rethrow;
    }
  }

  /// Initialise la caméra
  Future<void> _initializeCamera() async {
    try {
      final cameras = await availableCameras();
      if (cameras.isEmpty) {
        throw Exception('Aucune caméra disponible');
      }

      // Utiliser la caméra arrière par défaut
      final camera = cameras.firstWhere(
        (camera) => camera.lensDirection == CameraLensDirection.back,
        orElse: () => cameras.first,
      );

      _cameraController = CameraController(
        camera,
        ResolutionPreset.medium,
        enableAudio: false,
        imageFormatGroup: ImageFormatGroup.yuv420,
      );

      await _cameraController!.initialize();
      notifyListeners();
      debugPrint('Caméra initialisée avec succès');
    } catch (e) {
      debugPrint('Erreur lors de l\'initialisation de la caméra: $e');
      rethrow;
    }
  }

  /// Démarre la détection en temps réel
  Future<void> startDetection() async {
    if (!_isInitialized || _isDetecting || !isCameraReady) return;

    _isDetecting = true;
    notifyListeners();

    try {
      await _cameraController!.startImageStream(_processImage);
      debugPrint('Détection TFLite en temps réel démarrée');
    } catch (e) {
      debugPrint('Erreur lors du démarrage de la détection: $e');
      _isDetecting = false;
      notifyListeners();
    }
  }

  /// Arrête la détection
  Future<void> stopDetection() async {
    if (!_isDetecting) return;

    try {
      await _cameraController?.stopImageStream();
      _isDetecting = false;
      _detectedObjects.clear();
      notifyListeners();
      debugPrint('Détection TFLite en temps réel arrêtée');
    } catch (e) {
      debugPrint('Erreur lors de l\'arrêt de la détection: $e');
    }
  }

  /// Traite une image de la caméra pour la détection TFLite
  void _processImage(CameraImage cameraImage) async {
    if (!_isModelLoaded) return;

    try {
      // Exécuter la détection d'objets avec TFLite
      var recognitions = await Tflite.detectObjectOnFrame(
        bytesList:
            cameraImage.planes.map((plane) {
              return plane.bytes;
            }).toList(),
        imageHeight: cameraImage.height,
        imageWidth: cameraImage.width,
        imageMean: 127.5,
        imageStd: 127.5,
        rotation: 90, // Ajuster selon l'orientation
        numResultsPerClass: 2,
        threshold: 0.3,
      );

      // Convertir les résultats en objets DetectedObject
      final detections = _convertRecognitions(recognitions);

      // Mettre à jour les résultats
      _detectedObjects = detections;

      // Annoncer vocalement les objets détectés
      await _announceDetectedObjects(detections);

      notifyListeners();
    } catch (e) {
      debugPrint('Erreur lors du traitement de l\'image TFLite: $e');
    }
  }

  /// Convertit les résultats TFLite en objets DetectedObject
  List<DetectedObject> _convertRecognitions(List<dynamic>? recognitions) {
    if (recognitions == null || recognitions.isEmpty) return [];

    final detections = <DetectedObject>[];

    for (var recognition in recognitions) {
      final confidence = recognition['confidenceInClass'] ?? 0.0;
      final label = recognition['detectedClass'] ?? 'unknown';

      // Coordonnées de la boîte englobante
      final rect = recognition['rect'] ?? {};
      final x = rect['x'] ?? 0.0;
      final y = rect['y'] ?? 0.0;
      final w = rect['w'] ?? 0.0;
      final h = rect['h'] ?? 0.0;

      if (confidence > 0.3) {
        // Seuil de confiance
        detections.add(
          DetectedObject(
            className: label,
            confidence: confidence,
            x: x,
            y: y,
            width: w,
            height: h,
          ),
        );
      }
    }

    // Trier par confiance décroissante
    detections.sort((a, b) => b.confidence.compareTo(a.confidence));

    return detections.take(5).toList(); // Limiter à 5 détections
  }

  /// Annonce vocalement les objets détectés
  Future<void> _announceDetectedObjects(List<DetectedObject> detections) async {
    if (detections.isEmpty) return;

    final now = DateTime.now();

    // Prendre l'objet avec la plus haute confiance
    final bestDetection = detections.first;
    final objectName = _translateToFrench(bestDetection.className);

    // Éviter les annonces trop fréquentes du même objet
    if (now.difference(_lastAnnouncementTime) > _announcementCooldown &&
        _lastAnnouncedObject != objectName) {
      try {
        await _ttsService.speak('Je vois $objectName');
        _lastAnnouncementTime = now;
        _lastAnnouncedObject = objectName;
        debugPrint('Annonce vocale: $objectName');
      } catch (e) {
        debugPrint('Erreur lors de l\'annonce vocale: $e');
      }
    }
  }

  /// Traduit les noms d'objets en français
  String _translateToFrench(String englishName) {
    const translations = {
      'person': 'une personne',
      'bicycle': 'un vélo',
      'car': 'une voiture',
      'motorcycle': 'une moto',
      'airplane': 'un avion',
      'bus': 'un bus',
      'train': 'un train',
      'truck': 'un camion',
      'boat': 'un bateau',
      'traffic light': 'un feu de circulation',
      'fire hydrant': 'une bouche d\'incendie',
      'stop sign': 'un panneau stop',
      'parking meter': 'un parcmètre',
      'bench': 'un banc',
      'bird': 'un oiseau',
      'cat': 'un chat',
      'dog': 'un chien',
      'horse': 'un cheval',
      'sheep': 'un mouton',
      'cow': 'une vache',
      'elephant': 'un éléphant',
      'bear': 'un ours',
      'zebra': 'un zèbre',
      'giraffe': 'une girafe',
      'backpack': 'un sac à dos',
      'umbrella': 'un parapluie',
      'handbag': 'un sac à main',
      'tie': 'une cravate',
      'suitcase': 'une valise',
      'frisbee': 'un frisbee',
      'skis': 'des skis',
      'snowboard': 'un snowboard',
      'sports ball': 'un ballon',
      'kite': 'un cerf-volant',
      'baseball bat': 'une batte de baseball',
      'baseball glove': 'un gant de baseball',
      'skateboard': 'un skateboard',
      'surfboard': 'une planche de surf',
      'tennis racket': 'une raquette de tennis',
      'bottle': 'une bouteille',
      'wine glass': 'un verre à vin',
      'cup': 'une tasse',
      'fork': 'une fourchette',
      'knife': 'un couteau',
      'spoon': 'une cuillère',
      'bowl': 'un bol',
      'banana': 'une banane',
      'apple': 'une pomme',
      'sandwich': 'un sandwich',
      'orange': 'une orange',
      'broccoli': 'du brocoli',
      'carrot': 'une carotte',
      'hot dog': 'un hot-dog',
      'pizza': 'une pizza',
      'donut': 'un donut',
      'cake': 'un gâteau',
      'chair': 'une chaise',
      'couch': 'un canapé',
      'potted plant': 'une plante en pot',
      'bed': 'un lit',
      'dining table': 'une table à manger',
      'toilet': 'des toilettes',
      'tv': 'une télévision',
      'laptop': 'un ordinateur portable',
      'mouse': 'une souris',
      'remote': 'une télécommande',
      'keyboard': 'un clavier',
      'cell phone': 'un téléphone portable',
      'microwave': 'un micro-ondes',
      'oven': 'un four',
      'toaster': 'un grille-pain',
      'sink': 'un évier',
      'refrigerator': 'un réfrigérateur',
      'book': 'un livre',
      'clock': 'une horloge',
      'vase': 'un vase',
      'scissors': 'des ciseaux',
      'teddy bear': 'un ours en peluche',
      'hair drier': 'un sèche-cheveux',
      'toothbrush': 'une brosse à dents',
    };

    return translations[englishName.toLowerCase()] ?? englishName;
  }

  /// Prend une photo et effectue la détection
  Future<List<DetectedObject>> detectFromPhoto() async {
    if (!isCameraReady || !_isModelLoaded) {
      return [];
    }

    try {
      // Prendre une photo
      final image = await _cameraController!.takePicture();
      debugPrint('Photo prise pour la détection TFLite');

      // Exécuter la détection sur l'image
      var recognitions = await Tflite.detectObjectOnImage(
        path: image.path,
        model: "YOLO",
        imageMean: 0.0,
        imageStd: 255.0,
        threshold: 0.3,
        numResultsPerClass: 5,
      );

      return _convertRecognitions(recognitions);
    } catch (e) {
      debugPrint('Erreur lors de la détection sur photo: $e');
      return [];
    }
  }

  /// Libère les ressources
  @override
  void dispose() {
    stopDetection();
    _cameraController?.dispose();
    Tflite.close();
    super.dispose();
  }
}
