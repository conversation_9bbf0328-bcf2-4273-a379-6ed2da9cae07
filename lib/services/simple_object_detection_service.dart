import 'dart:io';
import 'dart:typed_data';
import 'dart:ui' as ui;
import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:tflite_flutter/tflite_flutter.dart';
import 'package:image/image.dart' as img;
import '../models/detection_result.dart';

/// Service de détection d'objets simplifié basé sur le tutoriel Google
class SimpleObjectDetectionService {
  // Modèle TensorFlow Lite
  Interpreter? _interpreter;
  List<String> _labels = [];
  bool _isInitialized = false;

  // Configuration du modèle
  static const int _inputSize = 320;
  static const double _threshold = 0.3;
  static const int _maxResults = 5;

  bool get isInitialized => _isInitialized;

  /// Initialise le service de détection d'objets
  Future<bool> initialize() async {
    if (_isInitialized) return true;

    try {
      debugPrint('Initialisation du service de détection d\'objets...');

      // Charger le modèle TensorFlow Lite
      await _loadModel();

      _isInitialized = true;
      debugPrint('Service de détection d\'objets initialisé avec succès');
      return true;
    } catch (e) {
      debugPrint('Erreur lors de l\'initialisation: $e');
      return false;
    }
  }

  /// Charge le modèle TensorFlow Lite
  Future<void> _loadModel() async {
    try {
      debugPrint('Chargement du modèle TensorFlow Lite...');

      // Charger le modèle depuis les assets
      _interpreter = await Interpreter.fromAsset('assets/models/model.tflite');

      // Charger les labels COCO
      _labels = _getCocoLabels();

      debugPrint('Modèle TensorFlow Lite chargé avec succès');
      debugPrint('Nombre de labels: ${_labels.length}');
    } catch (e) {
      debugPrint('Erreur lors du chargement du modèle: $e');
      rethrow;
    }
  }

  /// Retourne les labels COCO (80 classes)
  List<String> _getCocoLabels() {
    return [
      'person', 'bicycle', 'car', 'motorcycle', 'airplane', 'bus', 'train', 'truck',
      'boat', 'traffic light', 'fire hydrant', 'stop sign', 'parking meter', 'bench',
      'bird', 'cat', 'dog', 'horse', 'sheep', 'cow', 'elephant', 'bear', 'zebra',
      'giraffe', 'backpack', 'umbrella', 'handbag', 'tie', 'suitcase', 'frisbee',
      'skis', 'snowboard', 'sports ball', 'kite', 'baseball bat', 'baseball glove',
      'skateboard', 'surfboard', 'tennis racket', 'bottle', 'wine glass', 'cup',
      'fork', 'knife', 'spoon', 'bowl', 'banana', 'apple', 'sandwich', 'orange',
      'broccoli', 'carrot', 'hot dog', 'pizza', 'donut', 'cake', 'chair', 'couch',
      'potted plant', 'bed', 'dining table', 'toilet', 'tv', 'laptop', 'mouse',
      'remote', 'keyboard', 'cell phone', 'microwave', 'oven', 'toaster', 'sink',
      'refrigerator', 'book', 'clock', 'vase', 'scissors', 'teddy bear', 'hair drier',
      'toothbrush'
    ];
  }

  /// Effectue la détection d'objets sur une image
  Future<List<DetectionResult>> detectObjects(Uint8List imageBytes) async {
    if (!_isInitialized || _interpreter == null) {
      debugPrint('Service non initialisé');
      return [];
    }

    try {
      debugPrint('Début de la détection d\'objets...');

      // Étape 1: Créer TensorImage à partir des bytes
      final decodedImage = img.decodeImage(imageBytes);
      if (decodedImage == null) {
        debugPrint('Impossible de décoder l\'image');
        return [];
      }

      // Étape 2: Préprocesser l'image
      final inputImage = _preprocessImage(decodedImage);

      // Étape 3: Exécuter l'inférence
      final results = await _runInference(inputImage);

      debugPrint('Détection terminée. ${results.length} objets détectés.');
      return results;
    } catch (e) {
      debugPrint('Erreur lors de la détection: $e');
      return [];
    }
  }

  /// Préprocesse l'image pour le modèle
  Float32List _preprocessImage(img.Image image) {
    // Redimensionner l'image à la taille d'entrée du modèle
    final resizedImage = img.copyResize(image, width: _inputSize, height: _inputSize);
    
    // Convertir en Float32List et normaliser (0-1)
    final input = Float32List(_inputSize * _inputSize * 3);
    int pixelIndex = 0;
    
    for (int y = 0; y < _inputSize; y++) {
      for (int x = 0; x < _inputSize; x++) {
        final pixel = resizedImage.getPixel(x, y);
        input[pixelIndex++] = pixel.r / 255.0;
        input[pixelIndex++] = pixel.g / 255.0;
        input[pixelIndex++] = pixel.b / 255.0;
      }
    }
    
    return input;
  }

  /// Exécute l'inférence sur l'image
  Future<List<DetectionResult>> _runInference(Float32List inputImage) async {
    if (_interpreter == null) return [];

    try {
      // Préparer les tenseurs d'entrée
      final input = inputImage.reshape([1, _inputSize, _inputSize, 3]);
      
      // Préparer les tenseurs de sortie
      final outputs = <int, Object>{};
      
      // Pour un modèle de détection d'objets standard
      // Les dimensions exactes dépendent du modèle spécifique
      outputs[0] = Float32List(1 * 2535 * 85); // Ajuster selon le modèle
      
      // Exécuter l'inférence
      _interpreter!.runForMultipleInputs([input], outputs);
      
      // Post-traiter les résultats
      return _postProcessResults(outputs[0] as Float32List);
    } catch (e) {
      debugPrint('Erreur lors de l\'inférence: $e');
      return [];
    }
  }

  /// Post-traite les résultats de l'inférence
  List<DetectionResult> _postProcessResults(Float32List rawOutput) {
    final detections = <DetectionResult>[];
    
    try {
      // Traitement basique - à adapter selon le format de sortie du modèle
      final numDetections = rawOutput.length ~/ 85; // 85 = 4 (bbox) + 1 (conf) + 80 (classes)
      
      for (int i = 0; i < numDetections && detections.length < _maxResults; i++) {
        final offset = i * 85;
        
        if (offset + 84 < rawOutput.length) {
          // Extraire les coordonnées et la confiance
          final centerX = rawOutput[offset];
          final centerY = rawOutput[offset + 1];
          final width = rawOutput[offset + 2];
          final height = rawOutput[offset + 3];
          final objectConfidence = rawOutput[offset + 4];
          
          if (objectConfidence > _threshold) {
            // Trouver la classe avec la plus haute probabilité
            double maxClassProb = 0;
            int maxClassIndex = 0;
            
            for (int j = 0; j < 80; j++) {
              final classProb = rawOutput[offset + 5 + j];
              if (classProb > maxClassProb) {
                maxClassProb = classProb;
                maxClassIndex = j;
              }
            }
            
            final finalConfidence = objectConfidence * maxClassProb;
            
            if (finalConfidence > _threshold && maxClassIndex < _labels.length) {
              // Convertir les coordonnées normalisées en coordonnées d'écran
              final left = (centerX - width / 2) * _inputSize;
              final top = (centerY - height / 2) * _inputSize;
              final right = (centerX + width / 2) * _inputSize;
              final bottom = (centerY + height / 2) * _inputSize;
              
              final label = _labels[maxClassIndex];
              final text = '$label, ${(finalConfidence * 100).toInt()}%';
              
              detections.add(DetectionResult(
                boundingBox: Rect.fromLTRB(left, top, right, bottom),
                text: text,
                label: label,
                confidence: finalConfidence,
              ));
            }
          }
        }
      }
      
      // Trier par confiance décroissante
      detections.sort((a, b) => b.confidence.compareTo(a.confidence));
      
    } catch (e) {
      debugPrint('Erreur lors du post-traitement: $e');
    }
    
    return detections.take(_maxResults).toList();
  }

  /// Dessine les résultats de détection sur l'image
  Future<Uint8List> drawDetectionResults(Uint8List imageBytes, List<DetectionResult> detections) async {
    try {
      final decodedImage = img.decodeImage(imageBytes);
      if (decodedImage == null) return imageBytes;

      // Créer une copie de l'image pour dessiner dessus
      final outputImage = img.Image.from(decodedImage);

      for (final detection in detections) {
        final box = detection.boundingBox;
        
        // Ajuster les coordonnées à la taille de l'image
        final scaleX = outputImage.width / _inputSize;
        final scaleY = outputImage.height / _inputSize;
        
        final left = (box.left * scaleX).round();
        final top = (box.top * scaleY).round();
        final right = (box.right * scaleX).round();
        final bottom = (box.bottom * scaleY).round();
        
        // Dessiner le rectangle de délimitation
        img.drawRect(outputImage, 
          x1: left, 
          y1: top, 
          x2: right, 
          y2: bottom, 
          color: img.ColorRgb8(255, 0, 0), // Rouge
          thickness: 3
        );
        
        // Ajouter le texte (simplifié)
        // Note: img.drawString nécessite une police, on peut l'omettre pour l'instant
      }

      // Encoder l'image modifiée
      return Uint8List.fromList(img.encodePng(outputImage));
    } catch (e) {
      debugPrint('Erreur lors du dessin des résultats: $e');
      return imageBytes;
    }
  }

  /// Libère les ressources
  void dispose() {
    _interpreter?.close();
    _isInitialized = false;
  }
}
